import { Storage } from 'expo-sqlite/kv-store';

/**
 * 应用设置工具类
 * 用于管理应用的配置信息和默认值
 */
export class AppSettings {
  
  // 应用版本相关
  static async getAppVersion(): Promise<string> {
    return await Storage.getItem('app_version') || '0.5.0';
  }

  static async setAppVersion(version: string): Promise<void> {
    await Storage.setItem('app_version', version);
  }

  static async getAppBuild(): Promise<number> {
    const build = await Storage.getItem('app_build');
    return build ? parseInt(build, 10) : 50;
  }

  static async setAppBuild(build: number): Promise<void> {
    await Storage.setItem('app_build', build.toString());
  }

  // 开发者模式
  static async getIsDev(): Promise<boolean> {
    const isDev = await Storage.getItem('isDev');
    return isDev === 'true';
  }

  static async setIsDev(isDev: boolean): Promise<void> {
    await Storage.setItem('isDev', isDev.toString());
  }

  // 用户 Token
  static async getUserToken(): Promise<string | null> {
    return await Storage.getItem('userToken');
  }

  static async setUserToken(token: string): Promise<void> {
    await Storage.setItem('userToken', token);
  }

  static async clearUserToken(): Promise<void> {
    await Storage.removeItem('userToken');
  }

  // 是否只使用本地数据库
  static async getJustUseLocalSQLiteDB(): Promise<boolean> {
    const justUseLocal = await Storage.getItem('justUseLocalSQLiteDB');
    return justUseLocal === 'true';
  }

  static async setJustUseLocalSQLiteDB(justUseLocal: boolean): Promise<void> {
    await Storage.setItem('justUseLocalSQLiteDB', justUseLocal.toString());
  }

  // 检查应用是否已初始化
  static async isAppInitialized(): Promise<boolean> {
    const initialized = await Storage.getItem('app_initialized');
    return initialized === 'true';
  }

  // 获取所有设置信息（用于调试）
  static async getAllSettings(): Promise<{
    app_version: string;
    app_build: number;
    isDev: boolean;
    userToken: string | null;
    justUseLocalSQLiteDB: boolean;
    app_initialized: boolean;
  }> {
    return {
      app_version: await this.getAppVersion(),
      app_build: await this.getAppBuild(),
      isDev: await this.getIsDev(),
      userToken: await this.getUserToken(),
      justUseLocalSQLiteDB: await this.getJustUseLocalSQLiteDB(),
      app_initialized: await this.isAppInitialized(),
    };
  }

  // 重置所有设置（用于测试或重置应用）
  static async resetAllSettings(): Promise<void> {
    await Storage.removeItem('app_version');
    await Storage.removeItem('app_build');
    await Storage.removeItem('isDev');
    await Storage.removeItem('userToken');
    await Storage.removeItem('justUseLocalSQLiteDB');
    await Storage.removeItem('app_initialized');
    console.log('所有设置已重置');
  }

  // 打印所有设置（用于调试）
  static async logAllSettings(): Promise<void> {
    const settings = await this.getAllSettings();
    console.log('=== 应用设置信息 ===');
    console.log('应用版本:', settings.app_version);
    console.log('构建版本:', settings.app_build);
    console.log('开发者模式:', settings.isDev);
    console.log('用户Token:', settings.userToken ? '已设置' : '未设置');
    console.log('只使用本地数据库:', settings.justUseLocalSQLiteDB);
    console.log('应用已初始化:', settings.app_initialized);
    console.log('==================');
  }
}

// 导出默认实例
export default AppSettings;
