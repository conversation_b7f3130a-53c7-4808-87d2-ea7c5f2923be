import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  TextInput,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import AppSettings from '../utils/appSettings';

export default function AppSettingsScreen() {
  const insets = useSafeAreaInsets();
  
  // 状态管理
  const [settings, setSettings] = useState({
    app_version: '0.5.0',
    app_build: 50,
    isDev: false,
    userToken: null as string | null,
    justUseLocalSQLiteDB: true,
    app_initialized: false,
  });
  
  const [newUserToken, setNewUserToken] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // 加载设置
  const loadSettings = async () => {
    setIsLoading(true);
    try {
      const allSettings = await AppSettings.getAllSettings();
      setSettings(allSettings);
      setNewUserToken(allSettings.userToken || '');
    } catch (error) {
      console.error('加载设置失败:', error);
      Alert.alert('错误', '加载设置失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 切换开发者模式
  const toggleDevMode = async (value: boolean) => {
    try {
      await AppSettings.setIsDev(value);
      setSettings(prev => ({ ...prev, isDev: value }));
      Alert.alert('成功', `开发者模式已${value ? '开启' : '关闭'}`);
    } catch (error) {
      console.error('切换开发者模式失败:', error);
      Alert.alert('错误', '切换开发者模式失败');
    }
  };

  // 切换数据库模式
  const toggleDatabaseMode = async (value: boolean) => {
    try {
      await AppSettings.setJustUseLocalSQLiteDB(value);
      setSettings(prev => ({ ...prev, justUseLocalSQLiteDB: value }));
      Alert.alert('成功', `已切换到${value ? '本地' : '云端'}数据库模式`);
    } catch (error) {
      console.error('切换数据库模式失败:', error);
      Alert.alert('错误', '切换数据库模式失败');
    }
  };

  // 保存用户Token
  const saveUserToken = async () => {
    try {
      if (newUserToken.trim()) {
        await AppSettings.setUserToken(newUserToken.trim());
        setSettings(prev => ({ ...prev, userToken: newUserToken.trim() }));
        Alert.alert('成功', '用户Token已保存');
      } else {
        await AppSettings.clearUserToken();
        setSettings(prev => ({ ...prev, userToken: null }));
        Alert.alert('成功', '用户Token已清除');
      }
    } catch (error) {
      console.error('保存用户Token失败:', error);
      Alert.alert('错误', '保存用户Token失败');
    }
  };

  // 重置所有设置
  const resetAllSettings = () => {
    Alert.alert(
      '确认重置',
      '确定要重置所有设置吗？这将清除所有配置信息。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          style: 'destructive',
          onPress: async () => {
            try {
              await AppSettings.resetAllSettings();
              await loadSettings();
              Alert.alert('成功', '所有设置已重置');
            } catch (error) {
              console.error('重置设置失败:', error);
              Alert.alert('错误', '重置设置失败');
            }
          },
        },
      ]
    );
  };

  // 打印设置信息到控制台
  const logSettings = async () => {
    await AppSettings.logAllSettings();
    Alert.alert('成功', '设置信息已打印到控制台');
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const handleBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← 返回</Text>
        </TouchableOpacity>
        <Text style={styles.title}>应用设置</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* 应用信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>应用信息</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>版本号:</Text>
            <Text style={styles.infoValue}>{settings.app_version}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>构建版本:</Text>
            <Text style={styles.infoValue}>{settings.app_build}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>已初始化:</Text>
            <Text style={styles.infoValue}>{settings.app_initialized ? '是' : '否'}</Text>
          </View>
        </View>

        {/* 开发者设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>开发者设置</Text>
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>开发者模式</Text>
            <Switch
              value={settings.isDev}
              onValueChange={toggleDevMode}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={settings.isDev ? '#f5dd4b' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* 数据库设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>数据库设置</Text>
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>只使用本地数据库</Text>
            <Switch
              value={settings.justUseLocalSQLiteDB}
              onValueChange={toggleDatabaseMode}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={settings.justUseLocalSQLiteDB ? '#f5dd4b' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* 用户Token设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>用户Token</Text>
          <TextInput
            style={styles.tokenInput}
            value={newUserToken}
            onChangeText={setNewUserToken}
            placeholder="输入用户Token"
            multiline
          />
          <TouchableOpacity onPress={saveUserToken} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>保存Token</Text>
          </TouchableOpacity>
          <Text style={styles.tokenStatus}>
            当前状态: {settings.userToken ? '已设置' : '未设置'}
          </Text>
        </View>

        {/* 调试工具 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>调试工具</Text>
          <TouchableOpacity onPress={logSettings} style={styles.debugButton}>
            <Text style={styles.debugButtonText}>打印设置到控制台</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={resetAllSettings} style={styles.resetButton}>
            <Text style={styles.resetButtonText}>重置所有设置</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    marginRight: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingText: {
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 14,
    color: '#333',
  },
  tokenInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 12,
    fontSize: 14,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 12,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    borderRadius: 4,
    padding: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  tokenStatus: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  debugButton: {
    backgroundColor: '#34C759',
    borderRadius: 4,
    padding: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  debugButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  resetButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 4,
    padding: 12,
    alignItems: 'center',
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
});
